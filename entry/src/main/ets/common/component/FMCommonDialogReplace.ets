/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

import { CommonConstants } from '../constants/CommonConstants';
import { CommonBottomDialogButtonItemBean, CommonDialogAction, DialogAction } from './FMCommonBottomButtonDialog';
import { HosDialog } from '@feiniu/lib_component';
import { FMCommonListItemView } from './FMCommonListItemView';

interface FMCommonDialogParamNormal {
  // 0-单按钮，1-双按钮
  style?: number;

  // 提示内容
  title?: string | Resource;

  // 取消按钮文案
  cancelText?: string | Resource;

  // 取消按钮回调，默认关闭弹窗
  cancelAction?: (dialogAction: DialogAction) => void;

  // 确认按钮
  confirmText?: string | Resource;

  // 确认按钮回调
  confirmAction?: (dialogAction: DialogAction) => void;

  // 图片Url
  topImgUrl?: string;

  // 内容
  content?: string | Resource;
  contentColor?: ResourceColor;

  // 文本上边距
  contentTopMargin?: Length;

  // 如果内容需要Span组装，则使用此属性
  spanContentList?: FMCommonDialogSpanTextContent[];

  // 如果内容需要List组装，则使用此属性
  itemContentList?: FMCommonDialogListItemBean[];
  buttonTopMargin?: Length;

  // 弹窗的左右边距
  horizontalMargin?: Length;
}

/**
 * 打开弹窗
 * @param param 弹窗参数
 */
export function showFMCommonDialogNormal(paramNormal: FMCommonDialogParamNormal,
  alignment: Alignment = Alignment.Center) {
  let buttons: FMCommonDialogButtonItemBean[] = [];
  if (paramNormal.style === 0) {
    buttons.push(new FMCommonDialogButtonItemBean(paramNormal.cancelText ?? $r("app.string.common_dialog_cancel"),
      FMCommonDialogButtonItemBean.NORMAL, (index, dialogAction) => {
        paramNormal?.cancelAction?.(dialogAction)
      }));
  } else if (paramNormal.style === 1) {
    buttons.push(new FMCommonDialogButtonItemBean(paramNormal.cancelText ?? $r("app.string.common_dialog_cancel"),
      FMCommonDialogButtonItemBean.NORMAL, (index, dialogAction) => {
        paramNormal?.cancelAction?.(dialogAction)
      }));
    buttons.push(new FMCommonDialogButtonItemBean(paramNormal.confirmText ?? $r("app.string.common_dialog_confirm"),
      FMCommonDialogButtonItemBean.RED, (index, dialogAction) => {
        paramNormal?.confirmAction?.(dialogAction)
      }));
  }
  let text: FMCommonDialogContentText =
    new FMCommonDialogContentTextBean(paramNormal.content, paramNormal.contentColor, paramNormal.spanContentList,
      paramNormal.itemContentList, paramNormal.contentTopMargin);

  let param: FMCommonDialogParam = {
    styleButton: 0,
    title: paramNormal.title,
    topImgUrl: paramNormal.topImgUrl,
    contentData: text,
    customContentTextBuilderParam: text.contentComponent,
    beanList: buttons
  }

  showFMCommonDialog(param, alignment)

}

interface FMCommonDialogParam3In1 {
  // 0-横，1-竖
  styleButton?: number;

  // 提示内容
  title?: string | Resource;

  // 图片Url
  topImgUrl?: string;
  contentData?: FMCommonDialogContentText;

  // 底部按钮
  beanList: CommonBottomDialogButtonItemBean[];
  buttonTopMargin?: Length;

  // 弹窗的左右边距
  horizontalMargin?: Length;
}

/**
 * 打开弹窗
 * @param param 弹窗参数
 */
export function showFMCommonDialog3In1(param3In1: FMCommonDialogParam3In1,
  alignment: Alignment = Alignment.Center) {
  let contentData: FMCommonDialogContentText =
    new FMCommonDialogContentTextBean(param3In1.contentData?.content, param3In1.contentData?.contentColor,
      param3In1.contentData?.spanContentList, param3In1.contentData?.itemContentList,
      param3In1.contentData?.contentTopMargin);

  let param: FMCommonDialogParam = {
    styleButton: 1,
    title: param3In1.title,
    topImgUrl: param3In1.topImgUrl,
    contentData: contentData,
    customContentTextBuilderParam: contentData.contentComponent,
    buttonTopMargin: param3In1.buttonTopMargin,
    beanList: param3In1.beanList
  }
  showFMCommonDialog(param, alignment)
}


/**
 * 通用弹窗参数
 */
interface FMCommonDialogParam {
  // 排列方向 0-横向 平分，1-竖向 依次排列
  styleButton?: number;

  // 提示内容
  title?: string | Resource;

  // 图片Url
  topImgUrl?: string;
  contentData?: FMCommonDialogContentText;
  customContentTextBuilderParam?: (contentData?: FMCommonDialogContentText) => void;
  buttonTopMargin?: Length;

  // 弹窗的左右边距
  horizontalMargin?: Length;

  // 底部按钮
  beanList: CommonBottomDialogButtonItemBean[];
}

/**
 * 打开弹窗
 * @param param 弹窗参数
 */
export function showFMCommonDialog(param: FMCommonDialogParam,
  alignment: Alignment = Alignment.Center) {
  HosDialog.show({
    builder: componentDialog,
    builderArgs: param,
    alignment: alignment,
    clickMaskDismiss: true,
    backInterceptor: (bindRouter: string, tag: string) => {
      HosDialog.dismiss({ tag: tag })
      return true;
    }
  })
}

@Builder
function componentDialog(param: FMCommonDialogParam) {
  FMCommonDialog(param)
}

@Builder
function contentText(param?: FMCommonDialogContentText) {
  if (param?.content) {
    Text(param.content)
      .fontSize($r("app.float.font_size_14"))
      .fontColor((param.contentColor == null) ? $r("app.color.color_black") : param.contentColor)
      .margin({
        right: "16vp",
        left: "16vp",
        top: param.contentTopMargin
      })
  }
}

@Builder
function contentSpan(param?: FMCommonDialogContentText) {
  if (param?.spanContentList && param.spanContentList.length > 0) {
    Text() {
      ForEach(param.spanContentList, (item: FMCommonDialogSpanTextContent, index: number) => {
        Span(item.text)
          .fontSize(item.fontSize)
          .fontColor(item.fontColor)
      })
    }
    .margin({
      right: "16vp",
      left: "16vp",
      top: "15vp"
    })
  }
}

@Builder
function contentList(param?: FMCommonDialogContentText) {
  if (param?.itemContentList && param.itemContentList.length > 0) {
    List({ space: '5vp' }) {
      ForEach(param?.itemContentList, (item: FMCommonDialogListItemBean, position: number) => {
        ListItem() {
          FMCommonListItemView({ item })
        }
      })
    }
    .width(CommonConstants.FULL_PERCENT)
    .scrollBar(BarState.Off)
    .edgeEffect(EdgeEffect.None)
    // .height(this.getContentHeight())
    .margin({ top: "15vp" })
  }
}

export class FMCommonDialogSpanTextContent {
  constructor(text: string | Resource,
    fontColor: ResourceColor = $r("app.color.color_black"),
    fontSize: number | string | Resource = $r("app.float.font_size_14")) {
    this.text = text;
    this.fontSize = fontSize;
    this.fontColor = fontColor;
  }

  text: string | Resource = "";
  fontSize: number | string | Resource = $r("app.float.font_size_14");
  fontColor: ResourceColor = $r("app.color.color_black");
}

@Preview
export default struct FMCommonDialog {
  // 排列方向 0-横向 平分，1-竖向 依次排列
  styleButton?: number;
  // 提示内容
  title?: string | Resource;
  // 图片Url
  topImgUrl?: string;
  contentData?: FMCommonDialogContentText;
  @BuilderParam customContentTextBuilderParam: (contentData?: FMCommonDialogContentText) => void
  buttonTopMargin?: Length = 18;
  // 弹窗的左右边距
  horizontalMargin?: Length = 10;
  // 底部按钮
  beanList: CommonBottomDialogButtonItemBean[] = [];

  // bean?: CommonDialogBean
  // // 其他配置信息
  // @State list?: ExDataSource<CommonDialogListItemBean> = new ExDataSource<CommonDialogListItemBean>([]);

  aboutToAppear() {
    // this.list?.reload(this.bean?.list)
  }

  build() {
    Column() {
      Row() {
        Column() {
          Text(this.title === undefined ? "" : this.title)
            .fontSize($r('app.float.font_size_17'))
            .fontColor($r('app.color.color_black'))
            .margin({ top: '20vp' })
            .visibility(this.title === undefined ? Visibility.None : Visibility.Visible)
          Image(this.topImgUrl)
            .width('100%')
            .height('80vp')
            .objectFit(ImageFit.ScaleDown)
            .visibility(this.topImgUrl === undefined ? Visibility.None : Visibility.Visible)

          this.customContentTextBuilderParam(this.contentData)

          Flex({
            direction: this.styleButton === 0 ? FlexDirection.Row : FlexDirection.Column,
            wrap: FlexWrap.NoWrap,
          }) {
            ForEach(this.beanList, (item: FMCommonDialogButtonItemBean, index: number) => {
              FMCommonItemButtonView({ item, itemPosition: index, itemAction: item.clickAction })
                .margin(this.buttonMargin(this.styleButton, index, this.beanList.length))
                .width(this.styleButton === 0 ? "auto" : "100%")
                .layoutWeight(this.styleButton === 0 ? 1 : 0)
                .height(40)
            })
          }
          .width('100%')
          .padding({ left: '16vp', right: '16vp' })
          .margin({
            top: this.buttonTopMargin,
            bottom: '18vp',
            left: '10vp',
            right: '10vp'
          })

        }.width('100%')
        .padding({ right: "10vp", left: "10vp" })
        .justifyContent(FlexAlign.Center)
      }
      .width('100%')
      .borderRadius($r('app.float.common_dialog_radius'))
      .backgroundColor($r('app.color.color_white'))
      .justifyContent(FlexAlign.Start)
    }
    .margin({
      right: this.horizontalMargin,
      left: this.horizontalMargin
    })
    .backgroundColor($r('app.color.color_transparent'))
    .justifyContent(FlexAlign.Center)
  }

  /**
   * 按钮间距
   * @param styleButton
   * @param index
   * @param listSize
   * @returns
   */
  buttonMargin(styleButton: number | undefined, index: number, listSize: number): Padding | Length | LocalizedPadding {
    // 横排
    if (styleButton === 0) {
      // 中间按钮加间距
      if (index !== 0 && index !== listSize - 1) {
        return {
          left: '10vp',
          right: '10vp'
        }
      }
    } else {
      // 中间按钮加间距
      if (index !== 0 && index !== listSize - 1) {
        return {
          top: '10vp',
          bottom: '10vp'
        }
      }
    }
    return 0
  }

  // getContentHeight(): number {
  //   let length = this.bean?.list?.length ?? 0
  //   let resultLength = 0;
  //   if (length > 0) {
  //     for (let i = 0; i < length; i++) {
  //       resultLength += this.bean?.list?.[i].content?.length ?? 0
  //     }
  //   }
  //   let height = resultLength * 20 + 20
  //   if (height > 275) {
  //     height = 275
  //   }
  //   if (height < 45) {
  //     height = 45
  //   }
  //   return height;
  // }

}

/**
 * 三种按钮样式组件
 */
@Preview
@Component
struct FMCommonItemButtonView {
  item: FMCommonDialogButtonItemBean = new FMCommonDialogButtonItemBean("", FMCommonDialogButtonItemBean.NORMAL);
  itemPosition: number = 0;
  itemAction?: (position: number, action: DialogAction) => void;

  build() {
    if (this.item.style === FMCommonDialogButtonItemBean.NORMAL) {
      Button(this.item.title)
        .fontColor($r('app.color.color_e60012'))
        .backgroundColor($r('app.color.color_white'))
        .borderRadius("20vp")
        .borderWidth('1vp')
        .width('100%')
        .borderColor($r('app.color.color_e60012'))
        .onClick(() => {
          if (this.itemAction !== undefined) {
            this.itemAction(this.itemPosition, CommonDialogAction);
          }
        })
    } else if (this.item.style === FMCommonDialogButtonItemBean.RED) {
      Button(this.item.title)
        .borderRadius('20vp')
        .backgroundColor($r('app.color.color_ff3225'))
        .width('100%')
        .linearGradient({
          direction: GradientDirection.Right,
          repeating: false,
          colors: [
            [$r("app.color.color_ff3225"), 0],
            [$r("app.color.color_e60012"), 1]
          ]
        })
        .onClick(() => {
          if (this.itemAction !== undefined) {
            this.itemAction(this.itemPosition, CommonDialogAction);
          }
        })
    } else {
      Button(this.item.title)
        .fontColor($r('app.color.color_666666'))
        .backgroundColor($r('app.color.color_white'))
        .borderRadius("20vp")
        .width('100%')
        .borderWidth('1vp')
        .borderColor($r('app.color.color_bbbbbb'))
        .onClick(() => {
          if (this.itemAction !== undefined) {
            this.itemAction(this.itemPosition, CommonDialogAction);
          }
        })
    }
  }
}

export class FMCommonDialogButtonItemBean {
  public static NORMAL: number = 0
  public static RED: number = 1
  public static GRAY: number = 2
  public title: string | Resource = ""; // 按钮文案
  public style: number = 0; // 0 普通(红框白底)、1 破坏(红色)、2 失效(灰色)
  public clickAction?: (position: number, action: DialogAction) => void;

  constructor(title: string | Resource, style: number, clickAction?: (position: number, action: DialogAction) => void) {
    this.title = title;
    this.style = style;
    this.clickAction = clickAction;
  }
}

/**
 * 弹窗内容数据类
 * 支持三种类型 Text、Span、List
 */
export interface FMCommonDialogContentText {
  style?: number; // 0 正常Text组件、1 Span拼接Text、2 列表 Text
  // 内容
  content?: string | Resource;
  contentColor?: ResourceColor;

  // 如果内容需要Span组装，则使用此属性
  spanContentList?: FMCommonDialogSpanTextContent[];

  // 列表行
  itemContentList?: FMCommonDialogListItemBean[];

  // 文本上边距
  contentTopMargin?: Length
  contentComponent?: (contentData?: FMCommonDialogContentText) => void
}

export class FMCommonDialogContentTextBean implements FMCommonDialogContentText {
  public static Text: number = 0
  public static SPANS: number = 1
  public static LIST: number = 2
  public style?: number = 0; // 0 正常Text组件、1 Span拼接Text、2 列表 Text
  // 内容
  content?: string | Resource;
  contentColor?: ResourceColor;
  // 如果内容需要Span组装，则使用此属性
  spanContentList?: FMCommonDialogSpanTextContent[];
  // 列表行
  itemContentList?: FMCommonDialogListItemBean[];
  // 文本上边距
  contentTopMargin?: Length = 15;
  contentComponent?: (contentData?: FMCommonDialogContentText) => void = () => {
  };

  constructor(content?: string | Resource, contentColor?: ResourceColor,
    spanContentList?: FMCommonDialogSpanTextContent[], itemContentList?: FMCommonDialogListItemBean[],
    contentTopMargin?: Length) {
    this.content = content;
    this.contentColor = contentColor;
    this.spanContentList = spanContentList;
    this.itemContentList = itemContentList;
    if (contentTopMargin) {
      this.contentTopMargin = contentTopMargin;
    }
    // 自动解析数据类型并设置内容
    if (content) {
      this.style = FMCommonDialogContentTextBean.Text;
      this.contentComponent = contentText
    } else if (this.spanContentList && this.spanContentList.length > 0) {
      this.style = FMCommonDialogContentTextBean.SPANS;
      this.contentComponent = contentSpan
    } else if (this.itemContentList && this.itemContentList.length > 0) {
      this.style = FMCommonDialogContentTextBean.LIST;
      this.contentComponent = contentList
    }
  }
}

/**
 * 弹窗内文本列表使用
 */
export class FMCommonDialogListItemBean {
  public title?: string;
  public content?: string[];
}