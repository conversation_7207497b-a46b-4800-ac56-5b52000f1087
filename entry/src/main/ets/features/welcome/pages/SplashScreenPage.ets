/*
 * Copyright (c) 2024. 康成投资（中国）有限公司
 * http://www.rt-mart.com
 * 版权归本公司所有，不得私自使用、拷贝、修改、删除，否则视为侵权
 */

import { CommonConstants, PageParamConstants } from '../../../common/constants/CommonConstants';
import { PageConstants } from '../../../common/constants/PageConstants';
import { FMRequestBuilder } from '../../../common/utils/FMRequest';
import { APIConstants } from '../../../common/constants/APIConstants';
import { GuideBean } from '../bean/GuideBean';
import { ExCommonUtil, ExJsonUtils, ExLogger, ExSharePreferencesUtil, ExToastUtil } from '@feiniu/lib_utils';
import GlobalAddressUtils from '../../../common/helper/location/GlobalAddressUtils';
import { PrivacyModel } from '../model/PrivacyModel';
import { PrivacyBean } from '../bean/PrivacyBean';
import PrivacyDialog from '../component/PrivacyDialog';
import { FMEnvironment } from '../../../common/utils/FMEnvironment';
import { SharePreferencesConstants } from '../../../common/constants/SharePreferencesConstants';
import { AppInit } from '../../../common/utils/AppInit';
import PushUtils from '../../../common/utils/push/PushUtils';
import { ExRouterMgr } from '@feiniu/exrouter';
import { HosDialog, OhosInitDialog } from '@feiniu/lib_component';
import { StyleConstants } from '../../../common/constants/StyleConstants';
import { BreakpointState, BreakpointUIUtils } from '../../../common/utils/BreakpointUIUtils';
import FMCommonDialog from '../../../common/component/FMCommonDialog'
import {
  showFMCommonBottomButtonDialog,
  CommonBottomDialogButtonItemBean,
} from '../../../common/component/FMCommonBottomButtonDialog'
import { SimpleDialog } from '../../shopcart/dialog/SimpleDialog';
import { NormalDialog } from '../../settings/conponet/NormalDialog';
import { getString } from '../../../common/utils/InCommonUse';
import { CommonDialogBean, CommonDialogListItemBean } from '../../../common/bean/CommonDialogBean';
import {
  FMCommonDialogButtonItemBean,
  FMCommonDialogContentText,
  FMCommonDialogContentTextBean,
  FMCommonDialogSpanTextContent,
  showFMCommonDialog,
  showFMCommonDialog3In1,
  showFMCommonDialogNormal
} from '../../../common/component/FMCommonDialogReplace';
import { MyCustomDialog } from '../../../common/component/AIDialog1';

/**
 * 闪屏第一页
 */
@Entry
@Component
struct SplashScreenPage {
  @State pageShowTime: number = CommonConstants.TIME_DEFAULT_VALUE;
  @State intervalID: number = CommonConstants.INTERVAL_ID_DEFAULT;
  urlMain: string = PageConstants.PAGE_MAIN
  isReady: boolean = false
  isCountdown: boolean = false
  currentDialogController?: CustomDialogController
  controllerCustomCancel?: CustomDialogController
  controllerCustomExit?: CustomDialogController
  controllerCustomPrivacy?: CustomDialogController
  // 弹窗控制器
  commonDialogController?: CustomDialogController
  simpleDialogController?: CustomDialogController
  normalDialogController?: CustomDialogController
  @State imgBottomMargin: BreakpointState<Margin> = BreakpointState.of({
    sm: {
      left: 0,
      right: 0,
      bottom: "30%",
      top: "25%"
    } as Margin,
    md: {
      left: 0,
      right: 0,
      bottom: "16%",
      top: "25%"
    } as Margin
  })
  @State imgTopMargin: BreakpointState<Margin> = BreakpointState.of({
    sm: {
      left: 0,
      right: 0,
      bottom: "30%",
      top: "25%"
    } as Margin,
    md: {
      left: 0,
      right: 0,
      bottom: "30%",
      top: "10%"
    } as Margin
  })

  build() {
    // StatusBar({ bgColor: "#3300ff00" })
    Stack() {
      this.buildContainer()
      this.buildButtons()
      OhosInitDialog()
    }.width(StyleConstants.FULL_WIDTH)
    .layoutWeight(1)
  }

  @Builder
  buildContainer() {
    RelativeContainer() {
      Image($r('app.media.splash_bg'))
        .width(CommonConstants.FULL_PERCENT)
        .height(CommonConstants.FULL_PERCENT)
        .id("iv_bg")
      Image($r('app.media.splash_bottom_all'))
        .objectFit(ImageFit.BOTTOM)
        .id("iv_bottom")
        .margin(this.imgBottomMargin.value)

      Image($r('app.media.splash_top'))
        .objectFit(ImageFit.TOP)
        .id("iv_top")
        .margin(this.imgTopMargin.value)
    }
    .width(CommonConstants.FULL_PERCENT)
    .height(CommonConstants.FULL_PERCENT)
  }

  jumpMain() {
    // 准备好就开始跳转  todo delete
    if (this.isReady && this.isCountdown) {
      ExRouterMgr.replace({ pageUrl: this.urlMain });
    }
  }

  /**
   * When the SplashScreenPage is displayed, switch to the next page after 3 seconds.
   */
  onPageShow() {
    // todo 请添加路由周期容错判断
    if (this.currentDialogController) {
      this.currentDialogController.open()
    }

    this.intervalID = setInterval(() => {
      this.pageShowTime += CommonConstants.INCREMENT_VALUE;
      if (this.pageShowTime > CommonConstants.DELAY_SECONDS) {
        // 倒计时结束
        this.isCountdown = true;
        this.jumpMain()
        clearInterval(this.intervalID);
      }
    }, CommonConstants.INTERVAL_DELAY);


  }

  /**
   * When the SplashScreenPage is hide, clear interval.
   */
  onPageHide() {
    if (this.currentDialogController) {
      this.currentDialogController.close()
    }
    // 页面关闭暂停计时
    clearInterval(this.intervalID);
  }

  // 组件即将出现
  aboutToAppear() {

    // 有推送直接拦截跳转到首页， 隐私协议没同意直接忽略，走隐私弹框【大概率不会走到这】
    if (PushUtils.isPushPayload() && FMEnvironment.getInstance().hasAuthorized()) {
      this.initSdk();
      ExRouterMgr.replace({ pageUrl: this.urlMain });
      return;
    }
    new PrivacyModel().post((result?: PrivacyBean) => {
      if (!ExCommonUtil.isEmpty(result?.privacyVersion) && this.checkPrivacyVersion(result!)) {
        this.showPrivacyDialog(result!)
      } else {
        this.initSdk()
        // this.getWelcome()
      }
    }, (code, msg) => {
      // 这里第一次应该不会直接进来
      this.initSdk()
      // 跳转隐私协议
      // this.getWelcome()
    })
    this.onBreakpointStart()
  }

  onBreakpointStart() {
    BreakpointUIUtils.getInstance().attach(this.imgBottomMargin, this.imgTopMargin)
  }

  onBreakpointStop() {
    BreakpointUIUtils.getInstance().detach(this.imgBottomMargin, this.imgTopMargin)
  }

  initSdk() {
    AppInit.getInstance().initPrivateSDK()
  }

  // 组件即将消失
  aboutToDisappear() {
    // 页面关闭暂停计时
    clearInterval(this.intervalID);
    this.onBreakpointStop()

    // 关闭所有弹窗
    this.commonDialogController?.close();
    this.simpleDialogController?.close();
    this.normalDialogController?.close();
    HosDialog.dismiss(); // 关闭底部按钮弹窗
  }

  checkPrivacyVersion(result: PrivacyBean): boolean {
    let version = ExSharePreferencesUtil.getString(SharePreferencesConstants.APP_SHARE_PRIVACY_VERSION, "");
    return (version !== result.privacyVersion) && !ExCommonUtil.isEmpty(result.privacyVersion) &&
      !ExCommonUtil.isEmpty(result.privacyRuleBody)
  }

  showPrivacyDialog(result: PrivacyBean) {
    this.refreshVersion("");

    // 挽留弹窗1
    this.controllerCustomCancel = new CustomDialogController({
      builder: PrivacyDialog({
        cancelText: $r("app.string.splash_privacy_no_agree_again"),
        cancelAction: () => {
          this.controllerCustomExit?.open()
          this.currentDialogController = this.controllerCustomExit
        },
        confirmText: $r("app.string.splash_privacy_browse"),
        confirmAction: () => {
          this.controllerCustomPrivacy?.open()
          this.currentDialogController = this.controllerCustomPrivacy
        },
        title: $r("app.string.splash_privacy_again_tip"),
        content: $r("app.string.splash_privacy_again_content"),
      }
      ),
      onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
      },
      autoCancel: false,
      customStyle: true,
      alignment: DialogAlignment.Center
    });
    // 挽留弹窗2
    this.controllerCustomExit = new CustomDialogController({
      builder: PrivacyDialog({
        cancelText: $r("app.string.splash_privacy_quit_app"),
        cancelAction: () => {
          // 防止短时间内再次打开
          this.currentDialogController?.close()
          this.currentDialogController = this.controllerCustomPrivacy
          // 并不能直接退出最后的页面
          ExRouterMgr.pop()
        },
        confirmText: $r("app.string.splash_privacy_browse_again"),
        confirmAction: () => {
          this.controllerCustomPrivacy?.open()
          this.currentDialogController = this.controllerCustomPrivacy
        },
        title: $r("app.string.splash_privacy_think_tip"),
      }
      ),
      onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
      },
      autoCancel: false,
      customStyle: true,
      alignment: DialogAlignment.Center
    });
    // 隐私弹窗
    this.controllerCustomPrivacy = new CustomDialogController({
      builder: PrivacyDialog({
        cancelText: $r("app.string.splash_privacy_not_agree"),
        cancelAction: () => {
          this.controllerCustomCancel?.open()
          this.currentDialogController = this.controllerCustomCancel

        },
        confirmText: $r("app.string.splash_privacy_agree"),
        confirmAction: () => {
          this.currentDialogController = undefined
          this.refreshVersion(result.privacyVersion!)
          this.initSdk()
          // 跳转隐私协议
          // this.getWelcome()
        },
        title: $r("app.string.splash_privacy_warm_tip"),
        contentHtml: result.privacyRuleBody,
        contentHtmlBottom: result.privateRuleBottom,
      }
      ),
      onWillDismiss: (dismissDialogAction: DismissDialogAction) => {
      },
      autoCancel: false,
      customStyle: true,
      alignment: DialogAlignment.Center
    });

    this.controllerCustomPrivacy?.open();
    this.currentDialogController = this.controllerCustomPrivacy
  }

  private refreshVersion(version: string) {
    FMEnvironment.getInstance().refreshPrivacyVersion(version)
  }

  getWelcome() {
    let param: Record<string, string> = {};
    param["storeId"] = GlobalAddressUtils.getGlobalShopInfo().shopId;
    // 获取引导页数据
    FMRequestBuilder.newTypeSFBuilder<GuideBean>(APIConstants.getAPI().welcomePage)
      .setParams(param)
      .setPostSuccessCallBack((what: number, code: number, errorMsg?: string, result?: GuideBean) => {
        ExLogger.d(`setPostSuccessCallBack:what=${what},code=${code},errorMsg=${errorMsg},result=${ExJsonUtils.toJson(result)}`)

        if (!ExCommonUtil.isEmpty(result?.items)) {
          let map: Record<string, Object | number | undefined> = {};
          map[PageParamConstants.KEY_GUIDE_PARAM_ITEM] = result!.items;
          map[PageParamConstants.KEY_GUIDE_PARAM_SECOND] = result!.second;
          ExRouterMgr.replace({
            pageUrl: PageConstants.PAGE_GUILD,
            param: map
          });
        } else {
          this.isReady = true;
          this.jumpMain();
        }
      })
      .setPostFailedCallBack((what: number, code: number, errorMsg?: string, result?: GuideBean) => {
        ExLogger.d(`setPostFailedCallBack:what=${what},code=${code},errorMsg=${errorMsg},result=${ExJsonUtils.toJson(result)}`)

        this.isReady = true;
        this.jumpMain();
      })
      .setPostSuccessOrFailedCallBack((what: number, code: number, errorMsg?: string, result?: GuideBean,
        isBusinessCode?: boolean) => {
        ExLogger.d(`setPostSuccessOrFailedCallBack:what=${what},code=${code},errorMsg=${errorMsg},isBusinessCode=${isBusinessCode},result=${ExJsonUtils.toJson(result)}`)

      })
      .setStartOrFinishCallBack((what: number, isStart: boolean) => {
        ExLogger.d(`setStartOrFinishCallBack:what=${what},isStart=${isStart}`)
        if (isStart) {
          // 开始请求
        } else {
          // 请求结束
        }
      })
      .requestPost()
  }

  onBackPress(): boolean | void {
    return ExRouterMgr.onBackPress(this.queryRouterPageInfo(), this.getUIContext());
  }

  @Builder
  buildButtons() {
    Column() {
      // 通用对话框按钮
      Button("继续打开APP")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.getWelcome()) // 通用对话框按钮
      // 通用对话框按钮
      Button("Old通用：2按钮")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showCommonDialog()) // 通用对话框按钮
      Button("Old通用：1按钮")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showCommonDialog1()) // 通用对话框按钮
      Button("Old通用：拼接文本")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showCommonDialog2()) // 通用对话框按钮
      Button("Old通用：文本加粗多行")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showCommonDialog3())

      // 通用对话框按钮
      Button("通用：2按钮")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showNewCommonDialog()) // 通用对话框按钮
      Button("通用：1按钮")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showNewCommonDialog1()) // 通用对话框按钮
      Button("通用：拼接文本")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showNewCommonDialog2()) // 通用对话框按钮
      Button("通用：文本加粗多行")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showNewCommonDialog3())

      // 底部按钮对话框按钮  
      Button("打开底部按钮对话框")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showBottomButtonDialog())

      // 简单对话框按钮
      Button("打开简单对话框")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showSimpleDialog())

      // 普通对话框按钮
      Button("打开普通对话框")
        .width(300)
        .height(40)
        .margin(2)
        .onClick(() => this.showNormalDialog())
    }
    .width("100%")
    .alignItems(HorizontalAlign.Center)
  }

  /**
   * 显示通用对话框
   */
  showCommonDialog() {
    this.commonDialogController?.close();
    this.commonDialogController = new CustomDialogController({
      builder: FMCommonDialog({
        style: 1, // 双按钮样式
        title: "通用对话框",
        content: "这是一个通用对话框示例",
        cancelText: "取消",
        confirmText: "确定",
        cancelAction: () => {
          this.commonDialogController?.close();
        },
        confirmAction: () => {
          this.commonDialogController?.close();
        }
      }),
      autoCancel: true,
      customStyle: true,
      alignment: DialogAlignment.Center
    })
    this.commonDialogController.open();
  }

  /**
   * 显示通用对话框
   */
  showCommonDialog1() {
    this.commonDialogController?.close();
    this.commonDialogController = new CustomDialogController({
      builder: FMCommonDialog({
        style: 0, // 双按钮样式
        title: "通用对话框",
        content: "这是一个通用对话框示例",
        confirmText: "确定",
        confirmAction: () => {
          this.commonDialogController?.close();
        }
      }),
      autoCancel: true,
      customStyle: true,
      alignment: DialogAlignment.Center
    })
    this.commonDialogController.open();
  }

  /**
   * 显示通用对话框
   */
  showCommonDialog2() {
    this.commonDialogController?.close();
    this.commonDialogController = new CustomDialogController({
      builder: FMCommonDialog({
        style: 1,
        // title: $r("app.string.base_request_offline"),
        confirmText: $r("app.string.confirm1"),
        confirmAction: () => {
          this.commonDialogController?.close()
        },
        cancelAction: () => {
          this.commonDialogController?.close()
        },
        // content: msg,
        HorizontalMargin: "12vp",
        spanText: getString($r('app.string.integrate_center_exchange_coupon_notice_before')) + 13 +
        getString($r('app.string.integrate_center_exchange_coupon_notice_after')),
        spanStart: 7,
        spanEnd: 7 + (2 ?? 0),
        needSpanColor: $r('app.color.color_ff5277')
      }),
      autoCancel: true,
      customStyle: true,
      alignment: DialogAlignment.Center
    })
    this.commonDialogController.open();
  }

  /**
   * 显示通用对话框
   */
  showCommonDialog3() {
    let bean = new CommonDialogBean()
    let strList: string[] | undefined =
      ["asdasdasdmkkmk", "akmsondonhibahibsjkd"];

    bean.list = strList.map<CommonDialogListItemBean>((item, index, arr) => {
      let beanItem = new CommonDialogListItemBean()
      beanItem.title = "我是Item标题"
      beanItem.content = [item, item, item]
      return beanItem
    })
    this.commonDialogController = new CustomDialogController({
      builder: FMCommonDialog({
        HorizontalMargin: "37vp",
        style: 0,
        title: "title",
        bean: bean,
        confirmText: $r("app.string.msg_ok"),
        confirmAction: () => {
          this.commonDialogController?.close()
        }
      }),
      customStyle: true,
      alignment: DialogAlignment.Center,
      onWillDismiss: (action: DismissDialogAction) => {
      }
    });
    this.commonDialogController.open();
  }

  /**
   * 显示通用对话框
   */
  showNewCommonDialog() {

    showFMCommonDialogNormal({
      style: 1,
      title: "通用对话框",
      content: "这是一个通用对话框示例",
      cancelText: "取消",
      confirmText: "确定",
      cancelAction: (dialogAction) => {
        dialogAction.dismiss()
      },
      confirmAction: (dialogAction) => {
        dialogAction.dismiss()
      },
    })
  }

  /**
   * 显示通用对话框
   */
  showNewCommonDialog1() {
    showFMCommonDialogNormal({
      style: 0,
      title: "通用对话框",
      content: "这是一个通用对话框示例",
      confirmText: "确定",
      confirmAction: (dialogAction) => {
        dialogAction.dismiss()
      },
    })

  }

  /**
   * 显示通用对话框
   */
  showNewCommonDialog2() {
    showFMCommonDialogNormal({
      style: 1,
      // title: $r("app.string.base_request_offline"),
      confirmText: $r("app.string.confirm1"),
      confirmAction: (dialogAction) => {
        dialogAction.dismiss()
      },
      cancelAction: (dialogAction) => {
        dialogAction.dismiss()
      },
      // content: msg,
      horizontalMargin: "12vp",
      spanContentList: [
        new FMCommonDialogSpanTextContent(
          getString($r('app.string.integrate_center_exchange_coupon_notice_before')),
        ), new FMCommonDialogSpanTextContent(
        "13", $r('app.color.color_ff5277')
      ), new FMCommonDialogSpanTextContent(
        getString($r('app.string.integrate_center_exchange_coupon_notice_after')),
      ),
      ],
    })

  }

  /**
   * 显示通用对话框
   */
  showNewCommonDialog3() {
    let buttons: FMCommonDialogButtonItemBean[] = [];

    buttons.push(new FMCommonDialogButtonItemBean("第1个",
      FMCommonDialogButtonItemBean.NORMAL, (index, dialogAction) => {
        ExToastUtil.showShort("第1个")
        dialogAction.dismiss()
      }));
    buttons.push(new FMCommonDialogButtonItemBean("第2个",
      FMCommonDialogButtonItemBean.RED, (index, dialogAction) => {
        ExToastUtil.showShort("第2个")
        dialogAction.dismiss()
      }));
    buttons.push(new FMCommonDialogButtonItemBean("第3个",
      FMCommonDialogButtonItemBean.RED, (index, dialogAction) => {
        ExToastUtil.showShort(`第${index + 1}个`)
        dialogAction.dismiss()
      }));

    let contentData: FMCommonDialogContentText = {
      spanContentList: [
        new FMCommonDialogSpanTextContent(
          getString($r('app.string.integrate_center_exchange_coupon_notice_before'))
        ), new FMCommonDialogSpanTextContent(
        "13", $r('app.color.color_ff5277')
      ), new FMCommonDialogSpanTextContent(
        getString($r('app.string.integrate_center_exchange_coupon_notice_after'))
      ),
      ]
    };
    showFMCommonDialog3In1({
      styleButton: 1,
      title: $r("app.string.base_request_offline"),
      // content: msg,
      horizontalMargin: "12vp",
      contentData: contentData,
      beanList: buttons
    })

    // let bean = new CommonDialogBean()
    // let strList: string[] | undefined =
    //   ["asdasdasdmkkmk", "akmsondonhibahibsjkd"];
    //
    // bean.list = strList.map<CommonDialogListItemBean>((item, index, arr) => {
    //   let beanItem = new CommonDialogListItemBean()
    //   beanItem.title = "我是Item标题"
    //   beanItem.content = [item, item, item]
    //   return beanItem
    // })
    // this.commonDialogController = new CustomDialogController({
    //   builder: FMCommonDialog({
    //     HorizontalMargin: "37vp",
    //     style: 0,
    //     title: "title",
    //     bean: bean,
    //     confirmText: $r("app.string.msg_ok"),
    //     confirmAction: () => {
    //       this.commonDialogController?.close()
    //     }
    //   }),
    //   customStyle: true,
    //   alignment: DialogAlignment.Center,
    //   onWillDismiss: (action: DismissDialogAction) => {
    //   }
    // });
    // this.commonDialogController.open();
  }

  /**
   * 显示底部按钮对话框
   */
  showBottomButtonDialog() {
    let buttons: CommonBottomDialogButtonItemBean[] =
      [
        { title: "选项1", style: CommonBottomDialogButtonItemBean.BLACK },
        { title: "选项2", style: CommonBottomDialogButtonItemBean.BLACK },
        { title: "选项3", style: CommonBottomDialogButtonItemBean.BLACK }
      ];

    showFMCommonBottomButtonDialog({
      content: "这是一个底部按钮列表对话框示例",
      beanList: buttons,
      cancelText: "取消",
      cancelAction: (a) => {
        HosDialog.dismiss();
      },
      itemAction: (position, a) => {
        HosDialog.dismiss();
      }
    });
  }

  /**
   * 显示简单对话框
   * @deprecated
   */
  showSimpleDialog() {
    this.simpleDialogController?.close();
    this.simpleDialogController = new CustomDialogController({
      builder: SimpleDialog({
        title: "简单对话框",
        content: "我是简单对话框内容",
        cancelText: $r("app.string.dialog_abandon_prize_ok"),
        confirmText: $r("app.string.dialog_abandon_prize_continu"),
        onConfirm: () => {
          // 继续填写关闭弹窗
          this.simpleDialogController?.close();
        },
        onCancel: () => {
          this.simpleDialogController?.close();
        }
      }),
      autoCancel: true,
      customStyle: true,
      alignment: DialogAlignment.Center
    });
    this.simpleDialogController.open();
  }

  /**
   * 显示普通对话框
   * @deprecated
   */
  showNormalDialog() {
    this.normalDialogController?.close();
    this.normalDialogController = new CustomDialogController({
      builder: NormalDialog({
        cancel: "取消",
        confirm: "确定",
        cancelCallback: () => {
          this.normalDialogController?.close();
        },
        confirmCallback: () => {
          this.normalDialogController?.close();
        },
        title: "普通对话框",
        content: "这是一个普通对话框示例",
      }),
      autoCancel: true,
      customStyle: true,
      alignment: DialogAlignment.Center
    });
    this.normalDialogController.open();
  }

  private dialogController: CustomDialogController = new CustomDialogController({
    builder: MyCustomDialog(),
    alignment: DialogAlignment.Center, // 对话框居中显示
    autoCancel: true // 点击遮罩层可以关闭对话框
  })
}
